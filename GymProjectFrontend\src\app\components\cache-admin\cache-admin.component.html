<div class="container mt-4">
  <!-- Loading Spinner -->
  <div
    class="d-flex justify-content-center align-items-center"
    *ngIf="isLoading"
    style="height: 100vh"
  >
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Yükleniyor...</span>
    </div>
  </div>

  <div class="row" [class.content-blur]="isLoading">
    <div class="col-12">
      <div class="modern-card fade-in">
        <div class="modern-card-header">
          <div class="d-flex align-items-center gap-2">
            <h5 class="mb-0">
              <i class="fas fa-database me-2"></i>
              Cache Yönetim Si<PERSON>mi
            </h5>

            <!-- Help Button -->
            <app-help-button
              guideId="cache-admin"
              position="inline"
              size="small"
              tooltip="Bu panel hakkında yardım al">
            </app-help-button>
          </div>
          <div>
            <button class="modern-btn modern-btn-outline-primary modern-btn-sm me-2" (click)="refreshData()" [disabled]="isLoading">
              <i class="fas fa-sync-alt modern-btn-icon" [class.fa-spin]="isLoading"></i>
              Yenile
            </button>
            <button class="modern-btn modern-btn-success modern-btn-sm" (click)="testCache()">
              <i class="fas fa-check-circle modern-btn-icon"></i>
              Test
            </button>
          </div>
        </div>

        <!-- Tab Navigation -->
        <div class="modern-card-header border-top-0">
          <ul class="nav nav-tabs">
            <li class="nav-item">
              <a class="nav-link" [class.active]="activeTab === 'overview'" (click)="setActiveTab('overview')" href="javascript:void(0)">
                <i class="fas fa-tachometer-alt me-1"></i>Genel Bakış
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" [class.active]="activeTab === 'tenants'" (click)="setActiveTab('tenants')" href="javascript:void(0)">
                <i class="fas fa-users me-1"></i>Tenant'lar
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" [class.active]="activeTab === 'performance'" (click)="setActiveTab('performance')" href="javascript:void(0)">
                <i class="fas fa-chart-line me-1"></i>Performans
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" [class.active]="activeTab === 'management'" (click)="setActiveTab('management')" href="javascript:void(0)">
                <i class="fas fa-cogs me-1"></i>Yönetim
              </a>
            </li>
          </ul>
        </div>

        <div class="modern-card-body">
          <!-- Loading Spinner -->
          <div *ngIf="isLoading" class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Yükleniyor...</span>
            </div>
          </div>
          <!-- Overview Tab -->
          <div *ngIf="!isLoading && activeTab === 'overview'" class="fade-in">
            <!-- Quick Stats Cards -->
            <div class="row mb-4">
              <div class="col-md-3 col-sm-6 mb-3">
                <div class="modern-stats-card stats-card-blue zoom-in">
                  <div class="modern-stats-icon">
                    <i class="fas fa-users"></i>
                  </div>
                  <div class="modern-stats-info">
                    <div class="modern-stats-value">{{ activeTenants.length }}</div>
                    <div class="modern-stats-label">Aktif Tenant'lar</div>
                  </div>
                </div>
              </div>
              <div class="col-md-3 col-sm-6 mb-3">
                <div class="modern-stats-card stats-card-teal zoom-in">
                  <div class="modern-stats-icon">
                    <i class="fas fa-database"></i>
                  </div>
                  <div class="modern-stats-info">
                    <div class="modern-stats-value">{{ formatBytes(cacheSize) }}</div>
                    <div class="modern-stats-label">Toplam Cache</div>
                  </div>
                </div>
              </div>
              <div class="col-md-3 col-sm-6 mb-3">
                <div class="modern-stats-card stats-card-purple zoom-in">
                  <div class="modern-stats-icon">
                    <i class="fas fa-percentage"></i>
                  </div>
                  <div class="modern-stats-info">
                    <div class="modern-stats-value">{{ ((statistics?.hitRatio || 0) * 100) | number:'1.1-1' }}%</div>
                    <div class="modern-stats-label">Hit Oranı</div>
                  </div>
                </div>
              </div>
              <div class="col-md-3 col-sm-6 mb-3">
                <div class="modern-stats-card stats-card-orange zoom-in">
                  <div class="modern-stats-icon">
                    <i class="fas fa-memory"></i>
                  </div>
                  <div class="modern-stats-info">
                    <div class="modern-stats-value">{{ getMemoryUsagePercentage() | number:'1.1-1' }}%</div>
                    <div class="modern-stats-label">Bellek Kullanımı</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Detaylı İstatistikler -->
            <div *ngIf="statistics" class="row mb-4">
              <div class="col-12">
                <h6 class="mb-3">
                  <i class="fas fa-chart-bar me-2"></i>
                  Detaylı İstatistikler
                </h6>
              </div>
              <div class="col-md-3 col-sm-6 mb-3">
                <div class="modern-card slide-in-left">
                  <div class="modern-card-body text-center">
                    <div class="modern-badge modern-badge-info mb-2">
                      <i class="fas fa-bullseye me-1"></i>
                      Hit
                    </div>
                    <h4 class="text-info">{{ statistics.totalHits | number }}</h4>
                    <small class="text-muted">Başarılı İstek</small>
                  </div>
                </div>
              </div>
              <div class="col-md-3 col-sm-6 mb-3">
                <div class="modern-card slide-in-left">
                  <div class="modern-card-body text-center">
                    <div class="modern-badge modern-badge-warning mb-2">
                      <i class="fas fa-times-circle me-1"></i>
                      Miss
                    </div>
                    <h4 class="text-warning">{{ statistics.totalMisses | number }}</h4>
                    <small class="text-muted">Başarısız İstek</small>
                  </div>
                </div>
              </div>
              <div class="col-md-3 col-sm-6 mb-3">
                <div class="modern-card slide-in-right">
                  <div class="modern-card-body text-center">
                    <div class="modern-badge modern-badge-primary mb-2">
                      <i class="fas fa-key me-1"></i>
                      Anahtar
                    </div>
                    <h4 class="text-primary">{{ statistics.totalKeys | number }}</h4>
                    <small class="text-muted">Toplam Anahtar</small>
                  </div>
                </div>
              </div>
              <div class="col-md-3 col-sm-6 mb-3">
                <div class="modern-card slide-in-right">
                  <div class="modern-card-body text-center">
                    <div class="modern-badge modern-badge-danger mb-2">
                      <i class="fas fa-clock me-1"></i>
                      Expired
                    </div>
                    <h4 class="text-danger">{{ getExpiredKeysCount() | number }}</h4>
                    <small class="text-muted">Süresi Dolmuş</small>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Tenants Tab -->
          <div *ngIf="!isLoading && activeTab === 'tenants'" class="fade-in">
            <div class="row">
              <div class="col-12">
                <h6 class="mb-3">
                  <i class="fas fa-users me-2"></i>
                  Aktif Tenant'lar ({{ activeTenants.length }})
                </h6>
                <div class="row">
                  <div *ngFor="let tenantId of activeTenants; let i = index" class="col-md-6 col-lg-4 mb-3">
                    <div class="modern-card zoom-in" [style.animation-delay]="(i * 0.1) + 's'">
                      <div class="modern-card-header">
                        <h6 class="mb-0">
                          <span class="modern-badge modern-badge-primary me-2">{{ tenantId }}</span>
                          Tenant {{ tenantId }}
                        </h6>
                        <div class="dropdown">
                          <button class="modern-btn modern-btn-outline-primary modern-btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cog"></i>
                          </button>
                          <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="javascript:void(0)" (click)="loadTenantDetails(tenantId)">
                              <i class="fas fa-eye me-2"></i>Detayları Görüntüle
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="javascript:void(0)" (click)="clearTenantCacheById(tenantId)">
                              <i class="fas fa-trash me-2"></i>Cache'i Temizle
                            </a></li>
                          </ul>
                        </div>
                      </div>
                      <div class="modern-card-body">
                        <div class="row text-center">
                          <div class="col-6">
                            <div class="border-end">
                              <div class="modern-badge modern-badge-success mb-2">
                                <i class="fas fa-database me-1"></i>
                                Boyut
                              </div>
                              <h5 class="text-success">{{ getTenantCacheSize(tenantId) }}</h5>
                            </div>
                          </div>
                          <div class="col-6">
                            <div class="modern-badge modern-badge-info mb-2">
                              <i class="fas fa-key me-1"></i>
                              Anahtar
                            </div>
                            <h5 class="text-info">{{ getTenantKeyCount(tenantId) }}</h5>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Tenant yoksa gösterilecek mesaj -->
                <div *ngIf="activeTenants.length === 0" class="text-center py-5">
                  <i class="fas fa-users fa-3x mb-3 text-muted"></i>
                  <h5 class="text-muted">Aktif Tenant Bulunamadı</h5>
                  <p class="text-muted">Henüz cache kullanan bir tenant bulunmuyor.</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Performance Tab -->
          <div *ngIf="!isLoading && activeTab === 'performance'" class="fade-in">
            <div class="row">
              <div class="col-12">
                <h6 class="mb-3">
                  <i class="fas fa-chart-line me-2"></i>
                  Performans Metrikleri
                </h6>
              </div>
            </div>
            <div *ngIf="performanceMetrics" class="row">
              <div class="col-md-6 mb-4">
                <div class="modern-card slide-in-left">
                  <div class="modern-card-header">
                    <h6 class="mb-0">
                      <i class="fas fa-memory me-2"></i>
                      Bellek Kullanımı
                    </h6>
                  </div>
                  <div class="modern-card-body">
                    <div class="mb-3">
                      <div class="d-flex justify-content-between mb-2">
                        <span>Cache Belleği:</span>
                        <span class="modern-badge modern-badge-primary">{{ formatBytes(performanceMetrics.cacheMemoryUsage) }}</span>
                      </div>
                      <div class="progress mb-2" style="height: 8px;">
                        <div class="progress-bar" [ngClass]="getMemoryUsageColor()"
                             [style.width.%]="getMemoryUsagePercentage()">
                        </div>
                      </div>
                      <small class="text-muted">{{ getMemoryUsagePercentage() | number:'1.1-1' }}% kullanımda</small>
                    </div>
                    <div class="mb-3">
                      <div class="d-flex justify-content-between">
                        <span>Toplam Bellek:</span>
                        <span class="modern-badge modern-badge-info">{{ formatBytes(performanceMetrics.totalMemoryUsage) }}</span>
                      </div>
                    </div>
                    <div>
                      <div class="d-flex justify-content-between">
                        <span>Ortalama Anahtar Boyutu:</span>
                        <span class="modern-badge modern-badge-secondary">{{ formatBytes(performanceMetrics.averageKeySize) }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-md-6 mb-4">
                <div class="modern-card slide-in-right">
                  <div class="modern-card-header">
                    <h6 class="mb-0">
                      <i class="fas fa-tachometer-alt me-2"></i>
                      Cache Performansı
                    </h6>
                  </div>
                  <div class="modern-card-body">
                    <div class="mb-3">
                      <div class="d-flex justify-content-between mb-2">
                        <span>Hit Oranı:</span>
                        <span class="modern-badge" [ngClass]="getHitRatioColor(performanceMetrics.hitRatio)">
                          {{ (performanceMetrics.hitRatio * 100) | number:'1.1-1' }}%
                        </span>
                      </div>
                      <div class="progress mb-2" style="height: 8px;">
                        <div class="progress-bar" [ngClass]="getHitRatioColor(performanceMetrics.hitRatio)"
                             [style.width.%]="performanceMetrics.hitRatio * 100">
                        </div>
                      </div>
                    </div>
                    <div class="row text-center">
                      <div class="col-4">
                        <div class="modern-badge modern-badge-success mb-1">Hit</div>
                        <h6 class="text-success">{{ performanceMetrics.totalHits | number }}</h6>
                      </div>
                      <div class="col-4">
                        <div class="modern-badge modern-badge-warning mb-1">Miss</div>
                        <h6 class="text-warning">{{ performanceMetrics.totalMisses | number }}</h6>
                      </div>
                      <div class="col-4">
                        <div class="modern-badge modern-badge-danger mb-1">Expired</div>
                        <h6 class="text-danger">{{ performanceMetrics.expiredKeys | number }}</h6>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Management Tab -->
          <div *ngIf="!isLoading && activeTab === 'management'" class="fade-in">
            <div class="row">
              <div class="col-12">
                <h6 class="mb-3">
                  <i class="fas fa-cogs me-2"></i>
                  Cache Yönetim İşlemleri
                </h6>
              </div>
            </div>
            <div class="row mb-4">
              <div class="col-md-4 mb-3">
                <div class="modern-card slide-in-left">
                  <div class="modern-card-header">
                    <h6 class="mb-0">
                      <i class="fas fa-trash-alt me-2 text-danger"></i>
                      Tüm Cache'i Temizle
                    </h6>
                  </div>
                  <div class="modern-card-body">
                    <p class="text-muted mb-3">Sistemdeki tüm cache verilerini temizler.</p>
                    <button class="modern-btn modern-btn-danger w-100" (click)="clearAllCache()">
                      <i class="fas fa-trash-alt modern-btn-icon"></i>
                      Tümünü Temizle
                    </button>
                  </div>
                </div>
              </div>
              <div class="col-md-4 mb-3">
                <div class="modern-card slide-in-left" style="animation-delay: 0.1s;">
                  <div class="modern-card-header">
                    <h6 class="mb-0">
                      <i class="fas fa-broom me-2 text-warning"></i>
                      Tenant Cache Temizle
                    </h6>
                  </div>
                  <div class="modern-card-body">
                    <div class="modern-form-group">
                      <label class="modern-form-label">Tenant ID:</label>
                      <input type="number" class="modern-form-control" [(ngModel)]="selectedTenantId" placeholder="Tenant ID girin">
                    </div>
                    <button class="modern-btn modern-btn-warning w-100" (click)="clearTenantCache()" [disabled]="!selectedTenantId">
                      <i class="fas fa-broom modern-btn-icon"></i>
                      Tenant Cache Temizle
                    </button>
                  </div>
                </div>
              </div>
              <div class="col-md-4 mb-3">
                <div class="modern-card slide-in-right">
                  <div class="modern-card-header">
                    <h6 class="mb-0">
                      <i class="fas fa-eraser me-2 text-info"></i>
                      Entity Cache Temizle
                    </h6>
                  </div>
                  <div class="modern-card-body">
                    <div class="modern-form-group">
                      <label class="modern-form-label">Tenant ID:</label>
                      <input type="number" class="modern-form-control" [(ngModel)]="selectedTenantId" placeholder="Tenant ID girin">
                    </div>
                    <div class="modern-form-group">
                      <label class="modern-form-label">Entity Adı:</label>
                      <select class="modern-form-control" [(ngModel)]="selectedEntityName">
                        <option value="">Entity seçin</option>
                        <option *ngFor="let entity of entityNames" [value]="entity">{{ entity }}</option>
                      </select>
                    </div>
                    <button class="modern-btn modern-btn-info w-100" (click)="clearEntityCache()" [disabled]="!selectedTenantId || !selectedEntityName">
                      <i class="fas fa-eraser modern-btn-icon"></i>
                      Entity Cache Temizle
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Cache Keys Search -->
            <div class="row">
              <div class="col-12">
                <h6 class="mb-3">
                  <i class="fas fa-search me-2"></i>
                  Cache Anahtarları
                </h6>
                <div class="modern-card">
                  <div class="modern-card-header">
                    <div class="row align-items-center">
                      <div class="col-md-8">
                        <div class="input-group">
                          <input type="text" class="modern-form-control" [(ngModel)]="searchPattern"
                                 placeholder="Arama pattern'i girin (örn: T1:E_MembershipType:*)">
                          <button class="modern-btn modern-btn-primary" (click)="searchKeys()">
                            <i class="fas fa-search modern-btn-icon"></i>
                            Ara
                          </button>
                        </div>
                      </div>
                      <div class="col-md-4 text-end">
                        <span class="modern-badge modern-badge-secondary">{{ filteredKeys.length }} anahtar bulundu</span>
                      </div>
                    </div>
                  </div>
                  <div class="modern-card-body" style="max-height: 400px; overflow-y: auto;">
                    <div *ngIf="filteredKeys.length === 0" class="text-center text-muted py-5">
                      <i class="fas fa-search fa-3x mb-3"></i>
                      <h6 class="text-muted">Cache anahtarı bulunamadı</h6>
                      <p class="text-muted">Arama kriterlerinizi değiştirip tekrar deneyin.</p>
                    </div>
                    <div *ngFor="let key of filteredKeys; let i = index" class="border-bottom py-2 fade-in" [style.animation-delay]="(i * 0.05) + 's'">
                      <code class="text-break">{{ key }}</code>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Tenant Details Modal -->
          <div *ngIf="showTenantDetails" class="modal d-block fade-in" tabindex="-1" style="background-color: rgba(0,0,0,0.6);">
            <div class="modal-dialog modal-xl">
              <div class="modern-card zoom-in">
                <div class="modern-card-header">
                  <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    Tenant {{ selectedTenantForDetails }} Detayları
                  </h5>
                  <button type="button" class="modern-btn modern-btn-outline-secondary modern-btn-sm" (click)="closeTenantDetails()">
                    <i class="fas fa-times"></i>
                  </button>
                </div>
                <div class="modern-card-body">
                  <!-- Entity Counts -->
                  <div class="row mb-4">
                    <div class="col-12">
                      <h6 class="mb-3">
                        <i class="fas fa-chart-pie me-2"></i>
                        Entity Dağılımı
                      </h6>
                      <div class="row">
                        <div *ngFor="let entity of getEntityCountsArray(); let i = index" class="col-md-3 col-sm-6 mb-2">
                          <div class="modern-card slide-in-left" [style.animation-delay]="(i * 0.1) + 's'">
                            <div class="modern-card-body text-center">
                              <div class="modern-badge modern-badge-primary mb-2">{{ entity.name }}</div>
                              <h5 class="text-primary">{{ entity.count }}</h5>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Cache Items -->
                  <div class="row">
                    <div class="col-12">
                      <h6 class="mb-3">
                        <i class="fas fa-list me-2"></i>
                        Cache Öğeleri ({{ selectedTenantDetails.length }})
                      </h6>
                      <div class="modern-card">
                        <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                          <table class="table table-sm table-hover mb-0">
                            <thead class="table-dark sticky-top">
                              <tr>
                                <th>Anahtar</th>
                                <th>Entity</th>
                                <th>Boyut</th>
                                <th>Oluşturulma</th>
                                <th>Bitiş</th>
                                <th>TTL</th>
                                <th>Durum</th>
                              </tr>
                            </thead>
                            <tbody>
                              <tr *ngFor="let item of selectedTenantDetails; let i = index" class="fade-in" [style.animation-delay]="(i * 0.02) + 's'">
                                <td><code class="small text-break">{{ item.key }}</code></td>
                                <td><span class="modern-badge modern-badge-secondary">{{ item.entityName }}</span></td>
                                <td><span class="modern-badge modern-badge-info">{{ item.formattedSize }}</span></td>
                                <td class="small">{{ formatDateTime(item.createdAt) }}</td>
                                <td class="small">{{ formatDateTime(item.expiresAt) }}</td>
                                <td><span class="modern-badge modern-badge-warning">{{ item.formattedTTL }}</span></td>
                                <td><span class="modern-badge" [ngClass]="getStatusColor(item.status)">{{ item.status }}</span></td>
                              </tr>
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="modern-card-footer d-flex justify-content-end gap-2">
                  <button type="button" class="modern-btn modern-btn-secondary" (click)="closeTenantDetails()">
                    <i class="fas fa-times modern-btn-icon"></i>
                    Kapat
                  </button>
                  <button type="button" class="modern-btn modern-btn-danger" (click)="clearTenantCacheById(selectedTenantForDetails!)">
                    <i class="fas fa-trash modern-btn-icon"></i>
                    Cache'i Temizle
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

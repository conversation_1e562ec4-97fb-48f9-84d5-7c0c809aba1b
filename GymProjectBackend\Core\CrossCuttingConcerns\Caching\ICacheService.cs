namespace Core.CrossCuttingConcerns.Caching
{
    /// <summary>
    /// Cache service interface - Redis ve Memory cache için ortak arayüz
    /// SOLID prensiplerine uygun, generic type desteği ile
    /// Multi-tenant yapı için CompanyID isolation
    /// </summary>
    public interface ICacheService
    {
        /// <summary>
        /// Cache'den veri al - Generic type desteği
        /// </summary>
        /// <typeparam name="T">Dönüş tipi</typeparam>
        /// <param name="key">Cache key</param>
        /// <returns>Cache'deki veri veya default(T)</returns>
        T? Get<T>(string key);

        /// <summary>
        /// Cache'den veri al - Async
        /// </summary>
        /// <typeparam name="T">Dönüş tipi</typeparam>
        /// <param name="key">Cache key</param>
        /// <returns>Cache'deki veri veya default(T)</returns>
        Task<T?> GetAsync<T>(string key);

        /// <summary>
        /// Cache'e veri ekle - Generic type desteği
        /// </summary>
        /// <typeparam name="T">Veri tipi</typeparam>
        /// <param name="key">Cache key</param>
        /// <param name="value">Cache'lenecek veri</param>
        /// <param name="expiry">Expiry süresi (saniye), null ise default kullanılır</param>
        void Set<T>(string key, T value, int? expiry = null);

        /// <summary>
        /// Cache'e veri ekle - Async
        /// </summary>
        /// <typeparam name="T">Veri tipi</typeparam>
        /// <param name="key">Cache key</param>
        /// <param name="value">Cache'lenecek veri</param>
        /// <param name="expiry">Expiry süresi (saniye), null ise default kullanılır</param>
        Task SetAsync<T>(string key, T value, int? expiry = null);

        /// <summary>
        /// Cache'den veri sil
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <returns>Silme işlemi başarılı mı</returns>
        bool Remove(string key);

        /// <summary>
        /// Cache'den veri sil - Async
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <returns>Silme işlemi başarılı mı</returns>
        Task<bool> RemoveAsync(string key);

        /// <summary>
        /// Cache'de key var mı kontrol et
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <returns>Key var mı</returns>
        bool Exists(string key);

        /// <summary>
        /// Cache'de key var mı kontrol et - Async
        /// </summary>
        /// <param name="key">Cache key</param>
        /// <returns>Key var mı</returns>
        Task<bool> ExistsAsync(string key);

        /// <summary>
        /// Pattern'e göre key'leri getir
        /// </summary>
        /// <param name="pattern">Arama pattern'i (örn: "gym:1:*")</param>
        /// <returns>Bulunan key'ler</returns>
        IEnumerable<string> GetKeys(string pattern);

        /// <summary>
        /// Pattern'e göre key'leri getir - Async
        /// </summary>
        /// <param name="pattern">Arama pattern'i (örn: "gym:1:*")</param>
        /// <returns>Bulunan key'ler</returns>
        Task<IEnumerable<string>> GetKeysAsync(string pattern);

        /// <summary>
        /// Pattern'e göre toplu silme
        /// </summary>
        /// <param name="pattern">Silme pattern'i (örn: "gym:1:members:*")</param>
        /// <returns>Silinen key sayısı</returns>
        long RemoveByPattern(string pattern);

        /// <summary>
        /// Pattern'e göre toplu silme - Async
        /// </summary>
        /// <param name="pattern">Silme pattern'i (örn: "gym:1:members:*")</param>
        /// <returns>Silinen key sayısı</returns>
        Task<long> RemoveByPatternAsync(string pattern);

        /// <summary>
        /// Cache'i tamamen temizle (dikkatli kullan!)
        /// </summary>
        void Clear();

        /// <summary>
        /// Cache'i tamamen temizle - Async (dikkatli kullan!)
        /// </summary>
        Task ClearAsync();

        /// <summary>
        /// Redis bağlantı durumunu kontrol et
        /// </summary>
        /// <returns>Bağlantı aktif mi</returns>
        bool IsConnected();

        /// <summary>
        /// Redis ping testi
        /// </summary>
        /// <returns>Ping süresi (ms)</returns>
        Task<TimeSpan> PingAsync();

        /// <summary>
        /// Cache istatistikleri al
        /// </summary>
        /// <returns>Cache statistics</returns>
        Task<CacheStatistics> GetStatisticsAsync();

        /// <summary>
        /// Company bazlı cache temizleme
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <returns>Silinen key sayısı</returns>
        long ClearCompanyCache(int companyId);

        /// <summary>
        /// Company bazlı cache temizleme - Async
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <returns>Silinen key sayısı</returns>
        Task<long> ClearCompanyCacheAsync(int companyId);

        /// <summary>
        /// Entity bazlı cache temizleme
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <param name="entity">Entity adı</param>
        /// <returns>Silinen key sayısı</returns>
        long ClearEntityCache(int companyId, string entity);

        /// <summary>
        /// Entity bazlı cache temizleme - Async
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <param name="entity">Entity adı</param>
        /// <returns>Silinen key sayısı</returns>
        Task<long> ClearEntityCacheAsync(int companyId, string entity);

        /// <summary>
        /// Company cache istatistikleri al
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <returns>Company cache statistics</returns>
        Task<CompanyCacheStatistics> GetCompanyStatisticsAsync(int companyId);
    }

    /// <summary>
    /// Cache istatistik modeli
    /// </summary>
    public class CacheStatistics
    {
        public long TotalKeys { get; set; }
        public long UsedMemory { get; set; }
        public long MaxMemory { get; set; }
        public double HitRatio { get; set; }
        public long TotalConnections { get; set; }
        public bool IsConnected { get; set; }
        public TimeSpan Uptime { get; set; }
        public string Version { get; set; } = string.Empty;
    }

    /// <summary>
    /// Company cache istatistik modeli
    /// </summary>
    public class CompanyCacheStatistics
    {
        public int CompanyId { get; set; }
        public long TotalKeys { get; set; }
        public long EstimatedMemoryUsage { get; set; }
        public Dictionary<string, long> EntityCounts { get; set; } = new Dictionary<string, long>();
        public DateTime LastAccessed { get; set; }
        public List<string> RecentKeys { get; set; } = new List<string>();
    }
}

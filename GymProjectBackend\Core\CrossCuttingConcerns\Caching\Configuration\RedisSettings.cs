namespace Core.CrossCuttingConcerns.Caching.Configuration
{
    /// <summary>
    /// Redis bağlantı ve yapılandırma ayarları
    /// Multi-tenant yapı için environment bazlı konfigürasyon
    /// </summary>
    public class RedisSettings
    {
        /// <summary>
        /// Redis bağlantı string'i (host:port formatında)
        /// </summary>
        public string ConnectionString { get; set; } = "localhost:6379";

        /// <summary>
        /// Kullanılacak Redis database numarası (0-15 arası)
        /// Environment bazlı isolation için farklı database'ler
        /// </summary>
        public int Database { get; set; } = 0;

        /// <summary>
        /// Bağlantı timeout süresi (milisaniye)
        /// </summary>
        public int ConnectTimeout { get; set; } = 5000;

        /// <summary>
        /// Senkron operasyon timeout süresi (milisaniye)
        /// </summary>
        public int SyncTimeout { get; set; } = 5000;

        /// <summary>
        /// Bağlantı hatası durumunda retry sayısı
        /// </summary>
        public int ConnectRetry { get; set; } = 3;

        /// <summary>
        /// Keep-alive süresi (saniye)
        /// </summary>
        public int KeepAlive { get; set; } = 180;

        /// <summary>
        /// Varsayılan cache expiry süresi (saniye)
        /// </summary>
        public int DefaultExpiry { get; set; } = 3600;

        /// <summary>
        /// Redis şifresi (production için)
        /// </summary>
        public string? Password { get; set; }

        /// <summary>
        /// SSL kullanımı (production için)
        /// </summary>
        public bool UseSsl { get; set; } = false;

        /// <summary>
        /// Connection pool boyutu
        /// </summary>
        public int PoolSize { get; set; } = 10;

        /// <summary>
        /// Abort on connect fail
        /// </summary>
        public bool AbortOnConnectFail { get; set; } = false;
    }
}

using Core.Utilities.Security.CompanyContext;

namespace Core.CrossCuttingConcerns.Caching
{
    /// <summary>
    /// Cache service extension methods
    /// Multi-tenant cache operations için kolaylık sağlar
    /// </summary>
    public static class CacheExtensions
    {
        /// <summary>
        /// Entity cache'le (company context ile)
        /// </summary>
        /// <typeparam name="T">Entity tipi</typeparam>
        /// <param name="cacheService">Cache service</param>
        /// <param name="companyContext">Company context</param>
        /// <param name="entity">Entity adı</param>
        /// <param name="id">Entity ID</param>
        /// <param name="value">C<PERSON>'lenecek değer</param>
        /// <param name="expiry">Expiry süresi (saniye)</param>
        public static void SetEntity<T>(this ICacheService cacheService, ICompanyContext companyContext, 
            string entity, object id, T value, int? expiry = null)
        {
            var key = CacheKeyHelper.CreateKey(companyContext, entity, id);
            cacheService.Set(key, value, expiry);
        }

        /// <summary>
        /// Entity cache'le - Async (company context ile)
        /// </summary>
        /// <typeparam name="T">Entity tipi</typeparam>
        /// <param name="cacheService">Cache service</param>
        /// <param name="companyContext">Company context</param>
        /// <param name="entity">Entity adı</param>
        /// <param name="id">Entity ID</param>
        /// <param name="value">Cache'lenecek değer</param>
        /// <param name="expiry">Expiry süresi (saniye)</param>
        public static async Task SetEntityAsync<T>(this ICacheService cacheService, ICompanyContext companyContext, 
            string entity, object id, T value, int? expiry = null)
        {
            var key = CacheKeyHelper.CreateKey(companyContext, entity, id);
            await cacheService.SetAsync(key, value, expiry);
        }

        /// <summary>
        /// Entity cache'den al (company context ile)
        /// </summary>
        /// <typeparam name="T">Entity tipi</typeparam>
        /// <param name="cacheService">Cache service</param>
        /// <param name="companyContext">Company context</param>
        /// <param name="entity">Entity adı</param>
        /// <param name="id">Entity ID</param>
        /// <returns>Cache'deki değer</returns>
        public static T? GetEntity<T>(this ICacheService cacheService, ICompanyContext companyContext, 
            string entity, object id)
        {
            var key = CacheKeyHelper.CreateKey(companyContext, entity, id);
            return cacheService.Get<T>(key);
        }

        /// <summary>
        /// Entity cache'den al - Async (company context ile)
        /// </summary>
        /// <typeparam name="T">Entity tipi</typeparam>
        /// <param name="cacheService">Cache service</param>
        /// <param name="companyContext">Company context</param>
        /// <param name="entity">Entity adı</param>
        /// <param name="id">Entity ID</param>
        /// <returns>Cache'deki değer</returns>
        public static async Task<T?> GetEntityAsync<T>(this ICacheService cacheService, ICompanyContext companyContext, 
            string entity, object id)
        {
            var key = CacheKeyHelper.CreateKey(companyContext, entity, id);
            return await cacheService.GetAsync<T>(key);
        }

        /// <summary>
        /// Entity cache'den sil (company context ile)
        /// </summary>
        /// <param name="cacheService">Cache service</param>
        /// <param name="companyContext">Company context</param>
        /// <param name="entity">Entity adı</param>
        /// <param name="id">Entity ID</param>
        /// <returns>Silme başarılı mı</returns>
        public static bool RemoveEntity(this ICacheService cacheService, ICompanyContext companyContext, 
            string entity, object id)
        {
            var key = CacheKeyHelper.CreateKey(companyContext, entity, id);
            return cacheService.Remove(key);
        }

        /// <summary>
        /// Entity cache'den sil - Async (company context ile)
        /// </summary>
        /// <param name="cacheService">Cache service</param>
        /// <param name="companyContext">Company context</param>
        /// <param name="entity">Entity adı</param>
        /// <param name="id">Entity ID</param>
        /// <returns>Silme başarılı mı</returns>
        public static async Task<bool> RemoveEntityAsync(this ICacheService cacheService, ICompanyContext companyContext, 
            string entity, object id)
        {
            var key = CacheKeyHelper.CreateKey(companyContext, entity, id);
            return await cacheService.RemoveAsync(key);
        }

        /// <summary>
        /// Collection cache'le (company context ile)
        /// </summary>
        /// <typeparam name="T">Collection tipi</typeparam>
        /// <param name="cacheService">Cache service</param>
        /// <param name="companyContext">Company context</param>
        /// <param name="entity">Entity adı</param>
        /// <param name="value">Cache'lenecek collection</param>
        /// <param name="expiry">Expiry süresi (saniye)</param>
        /// <param name="parameters">Ek parametreler</param>
        public static void SetCollection<T>(this ICacheService cacheService, ICompanyContext companyContext, 
            string entity, T value, int? expiry = null, params object[] parameters)
        {
            var key = CacheKeyHelper.CreateCollectionKey(companyContext, entity, parameters);
            cacheService.Set(key, value, expiry);
        }

        /// <summary>
        /// Collection cache'le - Async (company context ile)
        /// </summary>
        /// <typeparam name="T">Collection tipi</typeparam>
        /// <param name="cacheService">Cache service</param>
        /// <param name="companyContext">Company context</param>
        /// <param name="entity">Entity adı</param>
        /// <param name="value">Cache'lenecek collection</param>
        /// <param name="expiry">Expiry süresi (saniye)</param>
        /// <param name="parameters">Ek parametreler</param>
        public static async Task SetCollectionAsync<T>(this ICacheService cacheService, ICompanyContext companyContext, 
            string entity, T value, int? expiry = null, params object[] parameters)
        {
            var key = CacheKeyHelper.CreateCollectionKey(companyContext, entity, parameters);
            await cacheService.SetAsync(key, value, expiry);
        }

        /// <summary>
        /// Collection cache'den al (company context ile)
        /// </summary>
        /// <typeparam name="T">Collection tipi</typeparam>
        /// <param name="cacheService">Cache service</param>
        /// <param name="companyContext">Company context</param>
        /// <param name="entity">Entity adı</param>
        /// <param name="parameters">Ek parametreler</param>
        /// <returns>Cache'deki collection</returns>
        public static T? GetCollection<T>(this ICacheService cacheService, ICompanyContext companyContext, 
            string entity, params object[] parameters)
        {
            var key = CacheKeyHelper.CreateCollectionKey(companyContext, entity, parameters);
            return cacheService.Get<T>(key);
        }

        /// <summary>
        /// Collection cache'den al - Async (company context ile)
        /// </summary>
        /// <typeparam name="T">Collection tipi</typeparam>
        /// <param name="cacheService">Cache service</param>
        /// <param name="companyContext">Company context</param>
        /// <param name="entity">Entity adı</param>
        /// <param name="parameters">Ek parametreler</param>
        /// <returns>Cache'deki collection</returns>
        public static async Task<T?> GetCollectionAsync<T>(this ICacheService cacheService, ICompanyContext companyContext, 
            string entity, params object[] parameters)
        {
            var key = CacheKeyHelper.CreateCollectionKey(companyContext, entity, parameters);
            return await cacheService.GetAsync<T>(key);
        }

        /// <summary>
        /// Pagination cache'le (company context ile)
        /// </summary>
        /// <typeparam name="T">Pagination result tipi</typeparam>
        /// <param name="cacheService">Cache service</param>
        /// <param name="companyContext">Company context</param>
        /// <param name="entity">Entity adı</param>
        /// <param name="pageNumber">Sayfa numarası</param>
        /// <param name="pageSize">Sayfa boyutu</param>
        /// <param name="value">Cache'lenecek pagination result</param>
        /// <param name="expiry">Expiry süresi (saniye)</param>
        /// <param name="searchTerm">Arama terimi</param>
        public static void SetPagination<T>(this ICacheService cacheService, ICompanyContext companyContext, 
            string entity, int pageNumber, int pageSize, T value, int? expiry = null, string? searchTerm = null)
        {
            var key = CacheKeyHelper.CreatePaginationKey(companyContext, entity, pageNumber, pageSize, searchTerm);
            cacheService.Set(key, value, expiry);
        }

        /// <summary>
        /// Pagination cache'le - Async (company context ile)
        /// </summary>
        /// <typeparam name="T">Pagination result tipi</typeparam>
        /// <param name="cacheService">Cache service</param>
        /// <param name="companyContext">Company context</param>
        /// <param name="entity">Entity adı</param>
        /// <param name="pageNumber">Sayfa numarası</param>
        /// <param name="pageSize">Sayfa boyutu</param>
        /// <param name="value">Cache'lenecek pagination result</param>
        /// <param name="expiry">Expiry süresi (saniye)</param>
        /// <param name="searchTerm">Arama terimi</param>
        public static async Task SetPaginationAsync<T>(this ICacheService cacheService, ICompanyContext companyContext, 
            string entity, int pageNumber, int pageSize, T value, int? expiry = null, string? searchTerm = null)
        {
            var key = CacheKeyHelper.CreatePaginationKey(companyContext, entity, pageNumber, pageSize, searchTerm);
            await cacheService.SetAsync(key, value, expiry);
        }

        /// <summary>
        /// Pagination cache'den al (company context ile)
        /// </summary>
        /// <typeparam name="T">Pagination result tipi</typeparam>
        /// <param name="cacheService">Cache service</param>
        /// <param name="companyContext">Company context</param>
        /// <param name="entity">Entity adı</param>
        /// <param name="pageNumber">Sayfa numarası</param>
        /// <param name="pageSize">Sayfa boyutu</param>
        /// <param name="searchTerm">Arama terimi</param>
        /// <returns>Cache'deki pagination result</returns>
        public static T? GetPagination<T>(this ICacheService cacheService, ICompanyContext companyContext, 
            string entity, int pageNumber, int pageSize, string? searchTerm = null)
        {
            var key = CacheKeyHelper.CreatePaginationKey(companyContext, entity, pageNumber, pageSize, searchTerm);
            return cacheService.Get<T>(key);
        }

        /// <summary>
        /// Pagination cache'den al - Async (company context ile)
        /// </summary>
        /// <typeparam name="T">Pagination result tipi</typeparam>
        /// <param name="cacheService">Cache service</param>
        /// <param name="companyContext">Company context</param>
        /// <param name="entity">Entity adı</param>
        /// <param name="pageNumber">Sayfa numarası</param>
        /// <param name="pageSize">Sayfa boyutu</param>
        /// <param name="searchTerm">Arama terimi</param>
        /// <returns>Cache'deki pagination result</returns>
        public static async Task<T?> GetPaginationAsync<T>(this ICacheService cacheService, ICompanyContext companyContext, 
            string entity, int pageNumber, int pageSize, string? searchTerm = null)
        {
            var key = CacheKeyHelper.CreatePaginationKey(companyContext, entity, pageNumber, pageSize, searchTerm);
            return await cacheService.GetAsync<T>(key);
        }

        /// <summary>
        /// Company cache'i temizle (company context ile)
        /// </summary>
        /// <param name="cacheService">Cache service</param>
        /// <param name="companyContext">Company context</param>
        /// <returns>Silinen key sayısı</returns>
        public static long ClearCompanyCache(this ICacheService cacheService, ICompanyContext companyContext)
        {
            var companyId = companyContext.GetCompanyId();
            return cacheService.ClearCompanyCache(companyId);
        }

        /// <summary>
        /// Company cache'i temizle - Async (company context ile)
        /// </summary>
        /// <param name="cacheService">Cache service</param>
        /// <param name="companyContext">Company context</param>
        /// <returns>Silinen key sayısı</returns>
        public static async Task<long> ClearCompanyCacheAsync(this ICacheService cacheService, ICompanyContext companyContext)
        {
            var companyId = companyContext.GetCompanyId();
            return await cacheService.ClearCompanyCacheAsync(companyId);
        }

        /// <summary>
        /// Entity cache'i temizle (company context ile)
        /// </summary>
        /// <param name="cacheService">Cache service</param>
        /// <param name="companyContext">Company context</param>
        /// <param name="entity">Entity adı</param>
        /// <returns>Silinen key sayısı</returns>
        public static long ClearEntityCache(this ICacheService cacheService, ICompanyContext companyContext, string entity)
        {
            var companyId = companyContext.GetCompanyId();
            return cacheService.ClearEntityCache(companyId, entity);
        }

        /// <summary>
        /// Entity cache'i temizle - Async (company context ile)
        /// </summary>
        /// <param name="cacheService">Cache service</param>
        /// <param name="companyContext">Company context</param>
        /// <param name="entity">Entity adı</param>
        /// <returns>Silinen key sayısı</returns>
        public static async Task<long> ClearEntityCacheAsync(this ICacheService cacheService, ICompanyContext companyContext, string entity)
        {
            var companyId = companyContext.GetCompanyId();
            return await cacheService.ClearEntityCacheAsync(companyId, entity);
        }
    }
}

using Core.Utilities.Security.CompanyContext;
using System.Text;

namespace Core.CrossCuttingConcerns.Caching
{
    /// <summary>
    /// Multi-tenant cache key helper
    /// Hierarchical key structure: "gym:{companyId}:{entity}:{id}"
    /// CompanyID bazlı cache isolation sağlar
    /// </summary>
    public static class CacheKeyHelper
    {
        private const string ROOT_PREFIX = "gym";
        private const string SEPARATOR = ":";

        /// <summary>
        /// Company bazlı cache key oluştur
        /// Format: "gym:{companyId}:{entity}:{id}"
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <param name="entity">Entity adı (member, payment, user vb.)</param>
        /// <param name="id">Entity ID</param>
        /// <returns>Hierarchical cache key</returns>
        public static string CreateKey(int companyId, string entity, object id)
        {
            if (companyId <= 0)
                throw new ArgumentException("Company ID must be greater than 0", nameof(companyId));
            
            if (string.IsNullOrWhiteSpace(entity))
                throw new ArgumentException("Entity name cannot be null or empty", nameof(entity));

            if (id == null)
                throw new ArgumentException("ID cannot be null", nameof(id));

            return $"{ROOT_PREFIX}{SEPARATOR}{companyId}{SEPARATOR}{entity.ToLowerInvariant()}{SEPARATOR}{id}";
        }

        /// <summary>
        /// Company bazlı cache key oluştur (ICompanyContext kullanarak)
        /// </summary>
        /// <param name="companyContext">Company context</param>
        /// <param name="entity">Entity adı</param>
        /// <param name="id">Entity ID</param>
        /// <returns>Hierarchical cache key</returns>
        public static string CreateKey(ICompanyContext companyContext, string entity, object id)
        {
            var companyId = companyContext.GetCompanyId();
            return CreateKey(companyId, entity, id);
        }

        /// <summary>
        /// Collection cache key oluştur
        /// Format: "gym:{companyId}:{entity}:list:{parameters}"
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <param name="entity">Entity adı</param>
        /// <param name="parameters">Ek parametreler (pagination, filter vb.)</param>
        /// <returns>Collection cache key</returns>
        public static string CreateCollectionKey(int companyId, string entity, params object[] parameters)
        {
            if (companyId <= 0)
                throw new ArgumentException("Company ID must be greater than 0", nameof(companyId));
            
            if (string.IsNullOrWhiteSpace(entity))
                throw new ArgumentException("Entity name cannot be null or empty", nameof(entity));

            var keyBuilder = new StringBuilder();
            keyBuilder.Append($"{ROOT_PREFIX}{SEPARATOR}{companyId}{SEPARATOR}{entity.ToLowerInvariant()}{SEPARATOR}list");

            if (parameters != null && parameters.Length > 0)
            {
                foreach (var param in parameters)
                {
                    if (param != null)
                    {
                        keyBuilder.Append($"{SEPARATOR}{param.ToString()?.ToLowerInvariant()}");
                    }
                }
            }

            return keyBuilder.ToString();
        }

        /// <summary>
        /// Collection cache key oluştur (ICompanyContext kullanarak)
        /// </summary>
        /// <param name="companyContext">Company context</param>
        /// <param name="entity">Entity adı</param>
        /// <param name="parameters">Ek parametreler</param>
        /// <returns>Collection cache key</returns>
        public static string CreateCollectionKey(ICompanyContext companyContext, string entity, params object[] parameters)
        {
            var companyId = companyContext.GetCompanyId();
            return CreateCollectionKey(companyId, entity, parameters);
        }

        /// <summary>
        /// Pagination cache key oluştur
        /// Format: "gym:{companyId}:{entity}:page:{pageNumber}:{pageSize}:{searchTerm}"
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <param name="entity">Entity adı</param>
        /// <param name="pageNumber">Sayfa numarası</param>
        /// <param name="pageSize">Sayfa boyutu</param>
        /// <param name="searchTerm">Arama terimi (opsiyonel)</param>
        /// <returns>Pagination cache key</returns>
        public static string CreatePaginationKey(int companyId, string entity, int pageNumber, int pageSize, string? searchTerm = null)
        {
            if (companyId <= 0)
                throw new ArgumentException("Company ID must be greater than 0", nameof(companyId));
            
            if (string.IsNullOrWhiteSpace(entity))
                throw new ArgumentException("Entity name cannot be null or empty", nameof(entity));

            var keyBuilder = new StringBuilder();
            keyBuilder.Append($"{ROOT_PREFIX}{SEPARATOR}{companyId}{SEPARATOR}{entity.ToLowerInvariant()}{SEPARATOR}page{SEPARATOR}{pageNumber}{SEPARATOR}{pageSize}");

            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                // Search term'i hash'le (uzun search term'ler için)
                var searchHash = searchTerm.GetHashCode().ToString();
                keyBuilder.Append($"{SEPARATOR}search{SEPARATOR}{searchHash}");
            }

            return keyBuilder.ToString();
        }

        /// <summary>
        /// Pagination cache key oluştur (ICompanyContext kullanarak)
        /// </summary>
        /// <param name="companyContext">Company context</param>
        /// <param name="entity">Entity adı</param>
        /// <param name="pageNumber">Sayfa numarası</param>
        /// <param name="pageSize">Sayfa boyutu</param>
        /// <param name="searchTerm">Arama terimi</param>
        /// <returns>Pagination cache key</returns>
        public static string CreatePaginationKey(ICompanyContext companyContext, string entity, int pageNumber, int pageSize, string? searchTerm = null)
        {
            var companyId = companyContext.GetCompanyId();
            return CreatePaginationKey(companyId, entity, pageNumber, pageSize, searchTerm);
        }

        /// <summary>
        /// Company pattern oluştur (toplu silme için)
        /// Format: "gym:{companyId}:*"
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <returns>Company pattern</returns>
        public static string CreateCompanyPattern(int companyId)
        {
            if (companyId <= 0)
                throw new ArgumentException("Company ID must be greater than 0", nameof(companyId));

            return $"{ROOT_PREFIX}{SEPARATOR}{companyId}{SEPARATOR}*";
        }

        /// <summary>
        /// Entity pattern oluştur (entity bazlı toplu silme için)
        /// Format: "gym:{companyId}:{entity}:*"
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <param name="entity">Entity adı</param>
        /// <returns>Entity pattern</returns>
        public static string CreateEntityPattern(int companyId, string entity)
        {
            if (companyId <= 0)
                throw new ArgumentException("Company ID must be greater than 0", nameof(companyId));
            
            if (string.IsNullOrWhiteSpace(entity))
                throw new ArgumentException("Entity name cannot be null or empty", nameof(entity));

            return $"{ROOT_PREFIX}{SEPARATOR}{companyId}{SEPARATOR}{entity.ToLowerInvariant()}{SEPARATOR}*";
        }

        /// <summary>
        /// Entity pattern oluştur (ICompanyContext kullanarak)
        /// </summary>
        /// <param name="companyContext">Company context</param>
        /// <param name="entity">Entity adı</param>
        /// <returns>Entity pattern</returns>
        public static string CreateEntityPattern(ICompanyContext companyContext, string entity)
        {
            var companyId = companyContext.GetCompanyId();
            return CreateEntityPattern(companyId, entity);
        }

        /// <summary>
        /// Cache key'den company ID'yi çıkar
        /// </summary>
        /// <param name="cacheKey">Cache key</param>
        /// <returns>Company ID veya -1 (geçersiz key)</returns>
        public static int ExtractCompanyId(string cacheKey)
        {
            if (string.IsNullOrWhiteSpace(cacheKey))
                return -1;

            var parts = cacheKey.Split(SEPARATOR);
            if (parts.Length < 3 || parts[0] != ROOT_PREFIX)
                return -1;

            if (int.TryParse(parts[1], out int companyId))
                return companyId;

            return -1;
        }

        /// <summary>
        /// Cache key'den entity adını çıkar
        /// </summary>
        /// <param name="cacheKey">Cache key</param>
        /// <returns>Entity adı veya null</returns>
        public static string? ExtractEntity(string cacheKey)
        {
            if (string.IsNullOrWhiteSpace(cacheKey))
                return null;

            var parts = cacheKey.Split(SEPARATOR);
            if (parts.Length < 4 || parts[0] != ROOT_PREFIX)
                return null;

            return parts[2];
        }

        /// <summary>
        /// Cache key'in geçerli olup olmadığını kontrol et
        /// </summary>
        /// <param name="cacheKey">Cache key</param>
        /// <returns>Geçerli mi</returns>
        public static bool IsValidKey(string cacheKey)
        {
            if (string.IsNullOrWhiteSpace(cacheKey))
                return false;

            var parts = cacheKey.Split(SEPARATOR);
            if (parts.Length < 4 || parts[0] != ROOT_PREFIX)
                return false;

            return int.TryParse(parts[1], out int companyId) && companyId > 0;
        }

        /// <summary>
        /// Yearly pattern oluştur (yıllık veriler için)
        /// Format: "gym:{companyId}:{entity}:{year}:*"
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <param name="entity">Entity adı</param>
        /// <param name="year">Yıl</param>
        /// <returns>Yearly pattern</returns>
        public static string CreateYearlyPattern(int companyId, string entity, int year)
        {
            if (companyId <= 0)
                throw new ArgumentException("Company ID must be greater than 0", nameof(companyId));
            
            if (string.IsNullOrWhiteSpace(entity))
                throw new ArgumentException("Entity name cannot be null or empty", nameof(entity));

            return $"{ROOT_PREFIX}{SEPARATOR}{companyId}{SEPARATOR}{entity.ToLowerInvariant()}{SEPARATOR}{year}{SEPARATOR}*";
        }

        /// <summary>
        /// Monthly pattern oluştur (aylık veriler için)
        /// Format: "gym:{companyId}:{entity}:{year}:{month}:*"
        /// </summary>
        /// <param name="companyId">Company ID</param>
        /// <param name="entity">Entity adı</param>
        /// <param name="year">Yıl</param>
        /// <param name="month">Ay</param>
        /// <returns>Monthly pattern</returns>
        public static string CreateMonthlyPattern(int companyId, string entity, int year, int month)
        {
            if (companyId <= 0)
                throw new ArgumentException("Company ID must be greater than 0", nameof(companyId));
            
            if (string.IsNullOrWhiteSpace(entity))
                throw new ArgumentException("Entity name cannot be null or empty", nameof(entity));

            return $"{ROOT_PREFIX}{SEPARATOR}{companyId}{SEPARATOR}{entity.ToLowerInvariant()}{SEPARATOR}{year}{SEPARATOR}{month:D2}{SEPARATOR}*";
        }
    }
}

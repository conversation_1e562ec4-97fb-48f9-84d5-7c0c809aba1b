﻿using Autofac.Core;
using Core.CrossCuttingConcerns.Logging.FileLogger;
using Core.CrossCuttingConcerns.Logging;
using Core.CrossCuttingConcerns.Caching;
using Core.CrossCuttingConcerns.Caching.Configuration;
using Core.Utilities.IoC;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using StackExchange.Redis;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Core.DependencyResolvers
{
    public class CoreModule : ICoreModule
    {
        public void Load(IServiceCollection serviceCollection)
        {
            // HTTP Context
            serviceCollection.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();

            // Company Context (Multi-tenant)
            serviceCollection.AddScoped<Core.Utilities.Security.CompanyContext.ICompanyContext, Core.Utilities.Security.CompanyContext.CompanyContext>();

            // Logging
            serviceCollection.AddSingleton<Stopwatch>();
            serviceCollection.AddSingleton<FileLoggerService>();
            serviceCollection.AddSingleton<PerformanceLoggerService>();
            serviceCollection.AddSingleton<ILogService, FileLoggerService>();

            // Redis Cache Configuration
            serviceCollection.AddSingleton<RedisSettings>(provider =>
            {
                var configuration = provider.GetRequiredService<IConfiguration>();
                var environment = configuration["Environment"] ?? "dev";
                var redisSettings = configuration.GetSection($"RedisSettings:{environment}").Get<RedisSettings>();

                if (redisSettings == null)
                {
                    throw new InvalidOperationException($"RedisSettings for environment '{environment}' not found in configuration");
                }

                return redisSettings;
            });

            // Redis Connection Factory
            serviceCollection.AddSingleton<RedisConnectionFactory>();

            // Redis Connection Multiplexer
            serviceCollection.AddSingleton<IConnectionMultiplexer>(provider =>
            {
                var factory = provider.GetRequiredService<RedisConnectionFactory>();
                return factory.GetConnection();
            });

            // Cache Service
            serviceCollection.AddSingleton<ICacheService, RedisCacheService>();
        }
    }
}

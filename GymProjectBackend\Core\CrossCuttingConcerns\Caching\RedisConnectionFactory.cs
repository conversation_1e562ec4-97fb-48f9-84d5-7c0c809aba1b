using Core.CrossCuttingConcerns.Caching.Configuration;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;

namespace Core.CrossCuttingConcerns.Caching
{
    /// <summary>
    /// Redis Connection Factory
    /// Singleton pattern ile connection multiplexer yönetimi
    /// Production-ready connection pooling ve error handling
    /// </summary>
    public class RedisConnectionFactory : IDisposable
    {
        private readonly RedisSettings _settings;
        private readonly ILogger<RedisConnectionFactory> _logger;
        private IConnectionMultiplexer? _connectionMultiplexer;
        private readonly object _lock = new object();
        private bool _disposed = false;

        public RedisConnectionFactory(RedisSettings settings, ILogger<RedisConnectionFactory> logger)
        {
            _settings = settings ?? throw new ArgumentNullException(nameof(settings));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Redis connection multiplexer'ı al
        /// Lazy initialization ile singleton pattern
        /// </summary>
        /// <returns>Connection multiplexer instance</returns>
        public IConnectionMultiplexer GetConnection()
        {
            if (_connectionMultiplexer != null && _connectionMultiplexer.IsConnected)
                return _connectionMultiplexer;

            lock (_lock)
            {
                if (_connectionMultiplexer != null && _connectionMultiplexer.IsConnected)
                    return _connectionMultiplexer;

                try
                {
                    // Eski connection'ı dispose et
                    _connectionMultiplexer?.Dispose();

                    // Yeni connection oluştur
                    var configurationOptions = CreateConfigurationOptions();
                    _connectionMultiplexer = ConnectionMultiplexer.Connect(configurationOptions);

                    // Event handler'ları ekle
                    RegisterEventHandlers(_connectionMultiplexer);

                    _logger.LogInformation("Redis connection established successfully. Endpoints: {Endpoints}",
                        string.Join(", ", _connectionMultiplexer.GetEndPoints().Select(ep => ep.ToString())));

                    return _connectionMultiplexer;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to establish Redis connection. ConnectionString: {ConnectionString}", 
                        _settings.ConnectionString);
                    throw new InvalidOperationException("Redis connection could not be established", ex);
                }
            }
        }

        /// <summary>
        /// Redis configuration options oluştur
        /// Production-ready ayarlar ile
        /// </summary>
        /// <returns>Configuration options</returns>
        private ConfigurationOptions CreateConfigurationOptions()
        {
            var config = new ConfigurationOptions
            {
                EndPoints = { _settings.ConnectionString },
                ConnectTimeout = _settings.ConnectTimeout,
                SyncTimeout = _settings.SyncTimeout,
                ConnectRetry = _settings.ConnectRetry,
                KeepAlive = _settings.KeepAlive,
                AbortOnConnectFail = _settings.AbortOnConnectFail,
                
                // Performance optimizations
                AsyncTimeout = _settings.SyncTimeout,
                // Development ortamında admin komutlarına izin ver
                AllowAdmin = true,
                
                // Connection pooling - StackExchange.Redis'te PoolSize property'si yok
                // Connection pooling otomatik olarak yönetiliyor
                
                // Resilience
                ReconnectRetryPolicy = new ExponentialRetry(1000, 30000),
                
                // Logging
                IncludeDetailInExceptions = true,
                IncludePerformanceCountersInExceptions = true
            };

            // SSL ayarları (production için)
            if (_settings.UseSsl)
            {
                config.Ssl = true;
                config.SslProtocols = System.Security.Authentication.SslProtocols.Tls12;
            }

            // Password ayarı
            if (!string.IsNullOrWhiteSpace(_settings.Password))
            {
                config.Password = _settings.Password;
            }

            return config;
        }

        /// <summary>
        /// Connection event handler'larını kaydet
        /// Monitoring ve logging için
        /// </summary>
        /// <param name="connection">Connection multiplexer</param>
        private void RegisterEventHandlers(IConnectionMultiplexer connection)
        {
            connection.ConnectionFailed += (sender, args) =>
            {
                _logger.LogError("Redis connection failed. Endpoint: {Endpoint}, FailureType: {FailureType}, Exception: {Exception}",
                    args.EndPoint, args.FailureType, args.Exception?.Message);
            };

            connection.ConnectionRestored += (sender, args) =>
            {
                _logger.LogInformation("Redis connection restored. Endpoint: {Endpoint}, FailureType: {FailureType}",
                    args.EndPoint, args.FailureType);
            };

            connection.ErrorMessage += (sender, args) =>
            {
                _logger.LogError("Redis error message. Endpoint: {Endpoint}, Message: {Message}",
                    args.EndPoint, args.Message);
            };

            connection.InternalError += (sender, args) =>
            {
                _logger.LogError(args.Exception, "Redis internal error. Endpoint: {Endpoint}, Origin: {Origin}",
                    args.EndPoint, args.Origin);
            };

            connection.ConfigurationChanged += (sender, args) =>
            {
                _logger.LogInformation("Redis configuration changed. Endpoint: {Endpoint}",
                    args.EndPoint);
            };

            connection.ConfigurationChangedBroadcast += (sender, args) =>
            {
                _logger.LogInformation("Redis configuration changed broadcast. Endpoint: {Endpoint}",
                    args.EndPoint);
            };
        }

        /// <summary>
        /// Connection durumunu kontrol et
        /// </summary>
        /// <returns>Bağlantı aktif mi</returns>
        public bool IsConnected()
        {
            return _connectionMultiplexer?.IsConnected ?? false;
        }

        /// <summary>
        /// Connection'ı test et
        /// </summary>
        /// <returns>Test başarılı mı</returns>
        public async Task<bool> TestConnectionAsync()
        {
            try
            {
                var connection = GetConnection();
                var database = connection.GetDatabase(_settings.Database);
                var pingResult = await database.PingAsync();
                
                _logger.LogInformation("Redis connection test successful. Ping: {Ping}ms", pingResult.TotalMilliseconds);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Redis connection test failed");
                return false;
            }
        }

        /// <summary>
        /// Connection bilgilerini al
        /// </summary>
        /// <returns>Connection info</returns>
        public ConnectionInfo GetConnectionInfo()
        {
            try
            {
                var connection = GetConnection();
                return new ConnectionInfo
                {
                    IsConnected = connection.IsConnected,
                    ClientName = connection.ClientName,
                    Configuration = connection.Configuration,
                    TimeoutMilliseconds = connection.TimeoutMilliseconds,
                    OperationCount = connection.OperationCount,
                    EndPoints = connection.GetEndPoints().Select(ep => ep.ToString()).ToArray()
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting connection info");
                return new ConnectionInfo { IsConnected = false };
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _connectionMultiplexer?.Dispose();
                _disposed = true;
                _logger.LogInformation("RedisConnectionFactory disposed");
            }
        }
    }

    /// <summary>
    /// Connection bilgi modeli
    /// </summary>
    public class ConnectionInfo
    {
        public bool IsConnected { get; set; }
        public string ClientName { get; set; } = string.Empty;
        public string Configuration { get; set; } = string.Empty;
        public int TimeoutMilliseconds { get; set; }
        public long OperationCount { get; set; }
        public string[] EndPoints { get; set; } = Array.Empty<string>();
    }
}

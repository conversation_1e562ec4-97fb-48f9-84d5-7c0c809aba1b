using Core.CrossCuttingConcerns.Caching.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using StackExchange.Redis;
using System.Net;

namespace Core.CrossCuttingConcerns.Caching
{
    /// <summary>
    /// Redis Cache Service Implementation
    /// Production-ready, multi-tenant, high-performance cache service
    /// 100K+ kullanıcı için optimize edilmiş
    /// </summary>
    public class RedisCacheService : ICacheService, IDisposable
    {
        private readonly IConnectionMultiplexer _connectionMultiplexer;
        private readonly IDatabase _database;
        private readonly IServer _server;
        private readonly RedisSettings _settings;
        private readonly ILogger<RedisCacheService> _logger;
        private readonly JsonSerializerSettings _jsonSettings;
        private bool _disposed = false;

        public RedisCacheService(IConnectionMultiplexer connectionMultiplexer, RedisSettings settings, ILogger<RedisCacheService> logger)
        {
            _connectionMultiplexer = connectionMultiplexer ?? throw new ArgumentNullException(nameof(connectionMultiplexer));
            _settings = settings ?? throw new ArgumentNullException(nameof(settings));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            _database = _connectionMultiplexer.GetDatabase(_settings.Database);
            _server = _connectionMultiplexer.GetServer(_connectionMultiplexer.GetEndPoints().First());

            // JSON serialization ayarları - performans için optimize
            _jsonSettings = new JsonSerializerSettings
            {
                TypeNameHandling = TypeNameHandling.Auto,
                DateFormatHandling = DateFormatHandling.IsoDateFormat,
                NullValueHandling = NullValueHandling.Ignore,
                DefaultValueHandling = DefaultValueHandling.Ignore
            };

            _logger.LogInformation("RedisCacheService initialized. Database: {Database}, Server: {Server}", 
                _settings.Database, _connectionMultiplexer.GetEndPoints().First());
        }

        public T? Get<T>(string key)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(key))
                    return default(T);

                var value = _database.StringGet(key);
                if (!value.HasValue)
                    return default(T);

                if (typeof(T) == typeof(string))
                    return (T)(object)value.ToString();

                return JsonConvert.DeserializeObject<T>(value, _jsonSettings);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cache key: {Key}", key);
                return default(T);
            }
        }

        public async Task<T?> GetAsync<T>(string key)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(key))
                    return default(T);

                var value = await _database.StringGetAsync(key);
                if (!value.HasValue)
                    return default(T);

                if (typeof(T) == typeof(string))
                    return (T)(object)value.ToString();

                return JsonConvert.DeserializeObject<T>(value, _jsonSettings);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cache key async: {Key}", key);
                return default(T);
            }
        }

        public void Set<T>(string key, T value, int? expiry = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(key) || value == null)
                    return;

                var serializedValue = typeof(T) == typeof(string) 
                    ? value.ToString() 
                    : JsonConvert.SerializeObject(value, _jsonSettings);

                var expiryTimeSpan = expiry.HasValue 
                    ? TimeSpan.FromSeconds(expiry.Value) 
                    : TimeSpan.FromSeconds(_settings.DefaultExpiry);

                _database.StringSet(key, serializedValue, expiryTimeSpan);
                
                _logger.LogDebug("Cache set: {Key}, Expiry: {Expiry}s", key, expiryTimeSpan.TotalSeconds);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting cache key: {Key}", key);
            }
        }

        public async Task SetAsync<T>(string key, T value, int? expiry = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(key) || value == null)
                    return;

                var serializedValue = typeof(T) == typeof(string) 
                    ? value.ToString() 
                    : JsonConvert.SerializeObject(value, _jsonSettings);

                var expiryTimeSpan = expiry.HasValue 
                    ? TimeSpan.FromSeconds(expiry.Value) 
                    : TimeSpan.FromSeconds(_settings.DefaultExpiry);

                await _database.StringSetAsync(key, serializedValue, expiryTimeSpan);
                
                _logger.LogDebug("Cache set async: {Key}, Expiry: {Expiry}s", key, expiryTimeSpan.TotalSeconds);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting cache key async: {Key}", key);
            }
        }

        public bool Remove(string key)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(key))
                    return false;

                var result = _database.KeyDelete(key);
                _logger.LogDebug("Cache remove: {Key}, Success: {Success}", key, result);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing cache key: {Key}", key);
                return false;
            }
        }

        public async Task<bool> RemoveAsync(string key)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(key))
                    return false;

                var result = await _database.KeyDeleteAsync(key);
                _logger.LogDebug("Cache remove async: {Key}, Success: {Success}", key, result);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing cache key async: {Key}", key);
                return false;
            }
        }

        public bool Exists(string key)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(key))
                    return false;

                return _database.KeyExists(key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking cache key existence: {Key}", key);
                return false;
            }
        }

        public async Task<bool> ExistsAsync(string key)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(key))
                    return false;

                return await _database.KeyExistsAsync(key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking cache key existence async: {Key}", key);
                return false;
            }
        }

        public IEnumerable<string> GetKeys(string pattern)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(pattern))
                    return Enumerable.Empty<string>();

                return _server.Keys(_settings.Database, pattern).Select(k => k.ToString());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting keys by pattern: {Pattern}", pattern);
                return Enumerable.Empty<string>();
            }
        }

        public async Task<IEnumerable<string>> GetKeysAsync(string pattern)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(pattern))
                    return Enumerable.Empty<string>();

                var keys = _server.KeysAsync(_settings.Database, pattern);
                var result = new List<string>();
                
                await foreach (var key in keys)
                {
                    result.Add(key.ToString());
                }
                
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting keys by pattern async: {Pattern}", pattern);
                return Enumerable.Empty<string>();
            }
        }

        public long RemoveByPattern(string pattern)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(pattern))
                    return 0;

                var keys = _server.Keys(_settings.Database, pattern).ToArray();
                if (keys.Length == 0)
                    return 0;

                var deletedCount = _database.KeyDelete(keys);
                _logger.LogInformation("Removed {Count} keys by pattern: {Pattern}", deletedCount, pattern);
                return deletedCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing keys by pattern: {Pattern}", pattern);
                return 0;
            }
        }

        public async Task<long> RemoveByPatternAsync(string pattern)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(pattern))
                    return 0;

                var keys = await GetKeysAsync(pattern);
                var keyArray = keys.Select(k => (RedisKey)k).ToArray();
                
                if (keyArray.Length == 0)
                    return 0;

                var deletedCount = await _database.KeyDeleteAsync(keyArray);
                _logger.LogInformation("Removed {Count} keys by pattern async: {Pattern}", deletedCount, pattern);
                return deletedCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing keys by pattern async: {Pattern}", pattern);
                return 0;
            }
        }

        public void Clear()
        {
            try
            {
                _server.FlushDatabase(_settings.Database);
                _logger.LogWarning("Cache database {Database} cleared!", _settings.Database);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing cache database: {Database}", _settings.Database);
            }
        }

        public async Task ClearAsync()
        {
            try
            {
                await _server.FlushDatabaseAsync(_settings.Database);
                _logger.LogWarning("Cache database {Database} cleared async!", _settings.Database);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing cache database async: {Database}", _settings.Database);
            }
        }

        public bool IsConnected()
        {
            try
            {
                return _connectionMultiplexer.IsConnected;
            }
            catch
            {
                return false;
            }
        }

        public async Task<TimeSpan> PingAsync()
        {
            try
            {
                return await _database.PingAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error pinging Redis");
                return TimeSpan.MaxValue;
            }
        }

        public async Task<CacheStatistics> GetStatisticsAsync()
        {
            try
            {
                var info = await _server.InfoAsync();
                var stats = new CacheStatistics();

                foreach (var group in info)
                {
                    foreach (var item in group)
                    {
                        switch (item.Key.ToLower())
                        {
                            case "used_memory":
                                if (long.TryParse(item.Value, out var usedMemory))
                                    stats.UsedMemory = usedMemory;
                                break;
                            case "maxmemory":
                                if (long.TryParse(item.Value, out var maxMemory))
                                    stats.MaxMemory = maxMemory;
                                break;
                            case "total_connections_received":
                                if (long.TryParse(item.Value, out var totalConnections))
                                    stats.TotalConnections = totalConnections;
                                break;
                            case "redis_version":
                                stats.Version = item.Value;
                                break;
                            case "uptime_in_seconds":
                                if (long.TryParse(item.Value, out var uptime))
                                    stats.Uptime = TimeSpan.FromSeconds(uptime);
                                break;
                        }
                    }
                }

                stats.TotalKeys = await _server.DatabaseSizeAsync(_settings.Database);
                stats.IsConnected = IsConnected();

                return stats;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting cache statistics");
                return new CacheStatistics { IsConnected = false };
            }
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _connectionMultiplexer?.Dispose();
                _disposed = true;
                _logger.LogInformation("RedisCacheService disposed");
            }
        }
    }
}
